"""
systemd Journal Collector

Collects logs from systemd journal.
"""

import subprocess
import json
import logging
from typing import List, Dict, Any
from .base_collector import BaseLogCollector


class JournalCollector(BaseLogCollector):
    """Collector for systemd journal logs."""

    def __init__(self, config: Dict[str, Any]):
        """Initialize journal collector."""
        super().__init__(config)
        self.logger = logging.getLogger(__name__)

        # Check if systemd-python is available
        self.has_systemd_python = False
        try:
            import systemd.journal
            self.has_systemd_python = True
            self.logger.info("systemd-python available, using Python bindings")
        except ImportError:
            self.logger.info("systemd-python not available, using journalctl command")

    def collect_logs(self) -> List[Dict[str, Any]]:
        """Collect journal logs using available method."""
        if self.has_systemd_python:
            return self._collect_with_python_bindings()
        else:
            return self._collect_with_journalctl()

    def _collect_with_python_bindings(self) -> List[Dict[str, Any]]:
        """Collect logs using systemd-python bindings."""
        # TODO: Implement systemd journal collection with Python bindings
        # This requires systemd-python package
        return []

    def _collect_with_journalctl(self) -> List[Dict[str, Any]]:
        """Collect logs using journalctl command."""
        try:
            # Build journalctl command
            cmd = ['journalctl', '--output=json', '--no-pager']

            # Add units filter if specified
            if 'units' in self.config and self.config['units']:
                for unit in self.config['units']:
                    cmd.extend(['--unit', unit])

            # Add since filter if specified
            if 'since' in self.config and self.config['since']:
                cmd.extend(['--since', self.config['since']])

            # Add lines limit
            cmd.extend(['--lines', '100'])

            # Execute journalctl
            result = subprocess.run(cmd, capture_output=True, text=True, timeout=30)

            if result.returncode != 0:
                self.logger.error(f"journalctl failed: {result.stderr}")
                return []

            # Parse JSON output
            logs = []
            for line in result.stdout.strip().split('\n'):
                if line.strip():
                    try:
                        log_entry = json.loads(line)
                        logs.append(log_entry)
                    except json.JSONDecodeError as e:
                        self.logger.warning(f"Failed to parse journal entry: {e}")

            return logs

        except subprocess.TimeoutExpired:
            self.logger.error("journalctl command timed out")
            return []
        except Exception as e:
            self.logger.error(f"Error collecting journal logs: {e}")
            return []
