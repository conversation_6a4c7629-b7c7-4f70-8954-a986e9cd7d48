# ExLog Linux Agent Installation Guide

This guide provides step-by-step instructions for installing and configuring the ExLog Linux Logging Agent.

## Prerequisites

### System Requirements

- **Operating System**: Linux (Ubuntu 18.04+, CentOS 7+, RHEL 7+, Debian 9+)
- **Python**: Python 3.7 or higher
- **Memory**: Minimum 128MB RAM (256MB recommended)
- **Disk Space**: 100MB for installation + log storage space
- **Network**: Access to ExLog Dashboard API endpoint

### Required Permissions

- **Root access** for system-wide installation and service management
- **Read access** to log files (typically requires root or specific group membership)
- **Network access** to ExLog Dashboard (default: port 5000)

## Installation Methods

### Method 1: Quick Installation (Recommended)

```bash
# Download and run the installation script
curl -sSL https://install.exlog.com/linux-agent.sh | sudo bash

# Or with wget
wget -qO- https://install.exlog.com/linux-agent.sh | sudo bash
```

### Method 2: Manual Installation

#### Step 1: Download the Agent

```bash
# Clone the repository
git clone https://github.com/exlog/linux-agent.git
cd linux-agent

# Or download and extract the release
wget https://github.com/exlog/linux-agent/releases/latest/download/exlog-linux-agent.tar.gz
tar -xzf exlog-linux-agent.tar.gz
cd exlog-linux-agent
```

#### Step 2: Install Dependencies

```bash
# Install Python dependencies
pip3 install -r requirements.txt

# Optional: Install systemd integration (requires system packages)
# sudo apt install libsystemd-dev pkg-config python3-dev
# pip3 install -r requirements-optional.txt

# Or install the package
sudo python3 setup.py install
```

#### Step 3: Create Configuration

```bash
# Create configuration directory
sudo mkdir -p /etc/exlog

# Copy default configuration
sudo cp config/default_config.yaml /etc/exlog/agent_config.yaml

# Edit configuration
sudo nano /etc/exlog/agent_config.yaml
```

#### Step 4: Install as Service

```bash
# Install systemd service
sudo python3 main.py service install

# Enable auto-start
sudo systemctl enable exlog-agent

# Start the service
sudo systemctl start exlog-agent
```

## Configuration

### Basic Configuration

Edit `/etc/exlog/agent_config.yaml`:

```yaml
# ExLog Dashboard API endpoint
api:
  enabled: true
  endpoint: "http://your-dashboard-server:5000"
  api_key: "your-api-key-here"
  
# Log collection sources
collection:
  syslog:
    enabled: true
    paths:
      - "/var/log/syslog"
      - "/var/log/messages"
  
  auth_logs:
    enabled: true
    paths:
      - "/var/log/auth.log"
      - "/var/log/secure"
```

### Dashboard Integration

1. **Get API Key**: Log into your ExLog Dashboard and generate an API key
2. **Configure Endpoint**: Set the correct dashboard URL in the configuration
3. **Test Connection**: Run `python3 main.py test api` to verify connectivity

### Advanced Configuration

#### Custom Log Sources

```yaml
collection:
  custom_logs:
    enabled: true
    paths:
      - "/var/log/myapp/*.log"
      - "/opt/application/logs/*.log"
```

#### Performance Tuning

```yaml
performance:
  max_cpu_percent: 10
  max_memory_mb: 256
  worker_threads: 2
  
general:
  buffer_size: 1000
  processing_interval: 5
```

#### Security Settings

```yaml
security:
  drop_privileges: true
  umask: "0022"
  
general:
  user: "exlog"
  group: "exlog"
```

## Service Management

### systemd Commands

```bash
# Start the service
sudo systemctl start exlog-agent

# Stop the service
sudo systemctl stop exlog-agent

# Restart the service
sudo systemctl restart exlog-agent

# Check status
sudo systemctl status exlog-agent

# View logs
sudo journalctl -u exlog-agent -f

# Enable auto-start
sudo systemctl enable exlog-agent

# Disable auto-start
sudo systemctl disable exlog-agent
```

### Manual Operation

```bash
# Run in foreground (for testing)
sudo python3 main.py run

# Run with debug logging
sudo python3 main.py run --log-level DEBUG

# Test configuration
python3 main.py config validate

# Test API connection
python3 main.py test api
```

## Verification

### Check Agent Status

```bash
# Service status
sudo systemctl status exlog-agent

# Agent logs
sudo tail -f /var/log/exlog/agent.log

# Check if logs are being collected
sudo ls -la /var/log/exlog/
```

### Verify Dashboard Integration

1. **Check Dashboard**: Log into ExLog Dashboard and verify agent appears in Agents page
2. **Check Logs**: Verify logs are appearing in the Logs page
3. **Test Alerts**: Configure test alerts to ensure notifications work

### Performance Monitoring

```bash
# Check resource usage
sudo systemctl status exlog-agent

# View detailed metrics
sudo cat /var/lib/exlog/metrics.json

# Monitor log collection
sudo tail -f /var/log/exlog/agent.log | grep "collected"
```

## Troubleshooting

### Common Issues

#### Agent Won't Start

```bash
# Check configuration
python3 main.py config validate

# Check permissions
sudo ls -la /var/log/syslog
sudo ls -la /etc/exlog/agent_config.yaml

# Check dependencies
pip3 list | grep -E "(PyYAML|requests|psutil)"
```

#### No Logs Being Collected

```bash
# Test collectors
python3 main.py test collectors

# Check file permissions
sudo ls -la /var/log/syslog /var/log/auth.log

# Verify log file paths
sudo find /var/log -name "*.log" -type f
```

#### API Connection Issues

```bash
# Test API connection
python3 main.py test api

# Check network connectivity
curl -v http://your-dashboard-server:5000/api/v1/health

# Verify API key
grep api_key /etc/exlog/agent_config.yaml
```

#### High Resource Usage

```bash
# Check current usage
sudo systemctl status exlog-agent

# Adjust configuration
sudo nano /etc/exlog/agent_config.yaml

# Reduce buffer size or processing frequency
# Limit CPU/memory in systemd service
```

### Log Files

- **Agent Logs**: `/var/log/exlog/agent.log`
- **Error Logs**: `/var/log/exlog/agent_errors.log`
- **System Logs**: `sudo journalctl -u exlog-agent`
- **Collected Logs**: `/var/log/exlog/standardized_logs.json`

### Getting Help

1. **Check Documentation**: Review README.md and configuration comments
2. **View Logs**: Check agent logs for error messages
3. **Test Components**: Use built-in test commands
4. **Contact Support**: Submit issues with log files and configuration

## Uninstallation

```bash
# Stop and disable service
sudo systemctl stop exlog-agent
sudo systemctl disable exlog-agent

# Uninstall service
sudo python3 main.py service uninstall

# Remove files
sudo rm -rf /etc/exlog
sudo rm -rf /var/log/exlog
sudo rm -rf /var/lib/exlog

# Remove package (if installed via pip)
pip3 uninstall exlog-linux-agent
```

## Security Considerations

- **Run with minimal privileges**: Configure user/group settings
- **Secure API keys**: Protect configuration files with appropriate permissions
- **Network security**: Use HTTPS for dashboard communication
- **Log sanitization**: Review logs for sensitive information
- **Regular updates**: Keep the agent updated with latest security patches
