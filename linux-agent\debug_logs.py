#!/usr/bin/env python3
"""
Debug Log Generation

Shows what logs are being collected and standardized by the agent.
"""

import sys
import json
import time
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config.config_manager import ConfigManager
from logging_agent.collectors.syslog_collector import SyslogCollector
from logging_agent.collectors.auth_collector import AuthLogCollector
from log_standardizer.standardizer import LogStandardizer


def test_log_collection():
    """Test log collection from various sources."""
    print("🔍 Debug Log Collection")
    print("=" * 50)
    
    try:
        # Load configuration
        config_manager = ConfigManager()
        config = config_manager.load_config()
        
        print("📋 Configuration loaded successfully")
        print(f"   API endpoint: {config['api']['endpoint']}")
        print(f"   API key: {config['api']['api_key'][:20]}...")
        print()
        
        # Initialize standardizer
        standardizer = LogStandardizer(config.get('standardization', {}))
        print("🔧 Log standardizer initialized")
        print()
        
        # Test syslog collection
        if config['collection']['syslog']['enabled']:
            print("📄 Testing syslog collection...")
            try:
                syslog_collector = SyslogCollector(config['collection']['syslog'])
                raw_logs = syslog_collector.collect_logs()
                
                print(f"   Raw logs collected: {len(raw_logs)}")
                
                if raw_logs:
                    print("   Sample raw log:")
                    print(f"   {json.dumps(raw_logs[0], indent=4)}")
                    
                    # Test standardization
                    standardized = standardizer.standardize_log(raw_logs[0])
                    if standardized:
                        print("   Sample standardized log:")
                        print(f"   {json.dumps(standardized, indent=4)}")
                    else:
                        print("   ❌ Failed to standardize log")
                else:
                    print("   ⚠️  No syslog entries found")
                    
            except Exception as e:
                print(f"   ❌ Syslog collection error: {e}")
            print()
        
        # Test auth log collection
        if config['collection']['auth_logs']['enabled']:
            print("🔐 Testing auth log collection...")
            try:
                auth_collector = AuthLogCollector(config['collection']['auth_logs'])
                raw_logs = auth_collector.collect_logs()
                
                print(f"   Raw auth logs collected: {len(raw_logs)}")
                
                if raw_logs:
                    print("   Sample raw auth log:")
                    print(f"   {json.dumps(raw_logs[0], indent=4)}")
                    
                    # Test standardization
                    standardized = standardizer.standardize_log(raw_logs[0])
                    if standardized:
                        print("   Sample standardized auth log:")
                        print(f"   {json.dumps(standardized, indent=4)}")
                else:
                    print("   ⚠️  No auth log entries found")
                    
            except Exception as e:
                print(f"   ❌ Auth log collection error: {e}")
            print()
        
        # Check log file paths
        print("📁 Checking log file accessibility...")
        syslog_paths = config['collection']['syslog']['paths']
        for path in syslog_paths:
            path_obj = Path(path)
            if path_obj.exists():
                print(f"   ✅ {path} - exists, size: {path_obj.stat().st_size} bytes")
            else:
                print(f"   ❌ {path} - not found")
        
        auth_paths = config['collection']['auth_logs']['paths']
        for path in auth_paths:
            path_obj = Path(path)
            if path_obj.exists():
                print(f"   ✅ {path} - exists, size: {path_obj.stat().st_size} bytes")
            else:
                print(f"   ❌ {path} - not found")
        
        print()
        print("💡 Tips:")
        print("   - If no logs are found, try generating some activity:")
        print("     sudo tail -f /var/log/syslog")
        print("     ssh localhost  # (will create auth logs)")
        print("   - Check file permissions if files exist but no logs collected")
        print("   - Run the agent with DEBUG log level for more details")
        
    except Exception as e:
        print(f"❌ Debug script error: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    test_log_collection()
