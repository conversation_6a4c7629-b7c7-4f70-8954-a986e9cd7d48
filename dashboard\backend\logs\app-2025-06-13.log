{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mWebSocket server started on port 5001\u001b[39m","timestamp":"2025-06-13 20:40:38:4038"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mMongoDB connected successfully\u001b[39m","timestamp":"2025-06-13 20:40:44:4044"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDatabase connected successfully\u001b[39m","timestamp":"2025-06-13 20:40:44:4044"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAll database connections established\u001b[39m","timestamp":"2025-06-13 20:40:44:4044"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAPI documentation enabled at /api/docs\u001b[39m","timestamp":"2025-06-13 20:40:44:4044"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mInitializing alert system...\u001b[39m","timestamp":"2025-06-13 20:40:44:4044"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mInitializing Correlation Engine...\u001b[39m","timestamp":"2025-06-13 20:40:44:4044"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mLoaded 0 alert rules into correlation engine\u001b[39m","timestamp":"2025-06-13 20:40:44:4044"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mCorrelation Engine initialized with 0 rules\u001b[39m","timestamp":"2025-06-13 20:40:44:4044"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mCreated system user for default rules\u001b[39m","timestamp":"2025-06-13 20:40:45:4045"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mInitializing default alert rules...\u001b[39m","timestamp":"2025-06-13 20:40:45:4045"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDefault rules initialization complete. Created: 8, Skipped: 0\u001b[39m","timestamp":"2025-06-13 20:40:45:4045"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mDefault alert rules initialized\u001b[39m","timestamp":"2025-06-13 20:40:45:4045"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAlert system initialization completed\u001b[39m","timestamp":"2025-06-13 20:40:45:4045"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mExLog API server running on port 5000\u001b[39m","timestamp":"2025-06-13 20:40:45:4045"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mEnvironment: development\u001b[39m","timestamp":"2025-06-13 20:40:45:4045"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mServer accessible on all network interfaces\u001b[39m","timestamp":"2025-06-13 20:40:45:4045"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mAlert rules changed, reloading...\u001b[39m","timestamp":"2025-06-13 20:41:14:4114"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mLoaded 8 alert rules into correlation engine\u001b[39m","timestamp":"2025-06-13 20:41:14:4114"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-13 20:44:32:4432"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-13 21:49:58:4958"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mUser logged in: <EMAIL>\u001b[39m","timestamp":"2025-06-13 21:53:52:5352"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mNew API key created: linux\u001b[39m","timestamp":"2025-06-13 21:58:01:581","userId":"684c8ccab26c1fcf6469e328"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mReceived SIGTERM, shutting down WebSocket server...\u001b[39m","timestamp":"2025-06-13 21:59:42:5942"}
{"level":"\u001b[32minfo\u001b[39m","message":"\u001b[32mReceived SIGTERM. Starting graceful shutdown...\u001b[39m","timestamp":"2025-06-13 21:59:43:5943"}
