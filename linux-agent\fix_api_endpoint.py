#!/usr/bin/env python3
"""
Fix API Endpoint Configuration

Updates the agent configuration to use the correct host IP address.
"""

import sys
import yaml
import socket
from pathlib import Path


def get_default_gateway():
    """Get the default gateway IP (likely the host machine)."""
    try:
        # Connect to a remote address to determine the local IP and gateway
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        local_ip = s.getsockname()[0]
        s.close()
        
        # Extract network portion and guess gateway
        ip_parts = local_ip.split('.')
        if ip_parts[0] == '192' and ip_parts[1] == '168':
            # Common home network
            gateway = f"{ip_parts[0]}.{ip_parts[1]}.{ip_parts[2]}.1"
        elif ip_parts[0] == '10':
            # Common corporate network
            gateway = f"{ip_parts[0]}.{ip_parts[1]}.{ip_parts[2]}.1"
        else:
            # Default guess
            gateway = f"{ip_parts[0]}.{ip_parts[1]}.{ip_parts[2]}.1"
            
        return gateway, local_ip
    except Exception:
        return None, None


def update_config_file(config_path, new_endpoint):
    """Update the configuration file with new endpoint."""
    try:
        with open(config_path, 'r') as f:
            config = yaml.safe_load(f)
        
        # Update the API endpoint
        if 'api' not in config:
            config['api'] = {}
        
        old_endpoint = config['api'].get('endpoint', 'not set')
        config['api']['endpoint'] = new_endpoint
        
        # Write back to file
        with open(config_path, 'w') as f:
            yaml.dump(config, f, default_flow_style=False, indent=2)
        
        print(f"✅ Configuration updated successfully!")
        print(f"   Old endpoint: {old_endpoint}")
        print(f"   New endpoint: {new_endpoint}")
        return True
        
    except Exception as e:
        print(f"❌ Error updating configuration: {e}")
        return False


def main():
    """Main function."""
    print("🔧 ExLog Agent API Endpoint Fix")
    print("=" * 40)
    
    # Check if running as root (needed to edit /etc/exlog/agent_config.yaml)
    import os
    if os.geteuid() != 0:
        print("❌ This script needs to be run as root (sudo)")
        print("   Usage: sudo python3 fix_api_endpoint.py [HOST_IP]")
        return 1
    
    # Get host IP from command line or auto-detect
    if len(sys.argv) > 1:
        host_ip = sys.argv[1]
        print(f"📍 Using provided host IP: {host_ip}")
    else:
        print("🔍 Auto-detecting host IP...")
        gateway, local_ip = get_default_gateway()
        if gateway:
            print(f"   VM IP: {local_ip}")
            print(f"   Detected gateway: {gateway}")
            
            # Ask user to confirm or provide correct IP
            response = input(f"   Use {gateway} as host IP? (y/n/custom): ").lower()
            if response == 'y':
                host_ip = gateway
            elif response == 'custom':
                host_ip = input("   Enter host IP address: ").strip()
            else:
                print("❌ Please run the script with the host IP as argument:")
                print("   sudo python3 fix_api_endpoint.py *************")
                return 1
        else:
            print("❌ Could not auto-detect host IP. Please provide it manually:")
            print("   sudo python3 fix_api_endpoint.py *************")
            return 1
    
    # Validate IP format
    try:
        socket.inet_aton(host_ip)
    except socket.error:
        print(f"❌ Invalid IP address format: {host_ip}")
        return 1
    
    # Configuration file path
    config_path = "/etc/exlog/agent_config.yaml"
    
    # Check if config file exists
    if not Path(config_path).exists():
        print(f"❌ Configuration file not found: {config_path}")
        print("   Make sure the agent is properly installed.")
        return 1
    
    # Create new endpoint
    new_endpoint = f"http://{host_ip}:5000"
    
    print(f"🔄 Updating configuration...")
    print(f"   Config file: {config_path}")
    print(f"   New endpoint: {new_endpoint}")
    
    # Update configuration
    if update_config_file(config_path, new_endpoint):
        print(f"\n🎉 Configuration updated successfully!")
        print(f"   You can now restart the agent:")
        print(f"   sudo python3 main.py run")
        print(f"\n💡 To test connectivity:")
        print(f"   python3 test_api_connectivity.py")
        return 0
    else:
        return 1


if __name__ == "__main__":
    sys.exit(main())
