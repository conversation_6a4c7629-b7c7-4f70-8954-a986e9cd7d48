#!/usr/bin/env python3
"""
Linux Syslog Monitoring Agent

A lightweight agent that monitors syslog files and sends new entries to a REST API.
"""

import os
import re
import json
import time
import socket
import logging
import argparse
import requests
import datetime
from pathlib import Path
from typing import Dict, List, Optional, Any

# Default configuration
DEFAULT_CONFIG = {
    "log_file": "/var/log/syslog",
    "api_endpoint": "http://localhost:3000/api/v1/logs",
    "api_key": "",
    "check_interval": 1.0,
    "max_batch_size": 100,
    "max_retries": 5,
    "backoff_factor": 2,
    "state_file": "~/.syslog_agent_state",
    "config_file": "~/.syslog_agent_config.json"
}

# Syslog parsing regex pattern
# This pattern matches common syslog formats
SYSLOG_PATTERN = re.compile(
    r'(?P<timestamp>\w{3}\s+\d{1,2}\s+\d{2}:\d{2}:\d{2})\s+'
    r'(?P<hostname>[\w\-\.]+)?\s+'
    r'(?P<process>[^:\[\]]+)(?:\[(?P<pid>\d+)\])?:\s+'
    r'(?P<message>.*)'
)

class SyslogAgent:
    """Agent for monitoring syslog files and sending entries to a REST API."""
    
    def __init__(self, config: Dict[str, Any]):
        """Initialize the agent with the given configuration."""
        self.config = config
        self.hostname = socket.gethostname()
        
        # Set up logging
        log_level = logging.DEBUG if config.get('debug', False) else logging.INFO
        log_format = '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        
        if config.get('log_to_file', False):
            log_file = config.get('error_log', '/var/log/agent_errors.log')
            os.makedirs(os.path.dirname(log_file), exist_ok=True)
            logging.basicConfig(
                level=log_level,
                format=log_format,
                filename=log_file,
                filemode='a'
            )
        else:
            logging.basicConfig(
                level=log_level,
                format=log_format
            )
            
        self.logger = logging.getLogger('syslog-agent')
        
        # Expand paths
        self.config['log_file'] = os.path.expanduser(self.config['log_file'])
        self.config['state_file'] = os.path.expanduser(self.config['state_file'])
        self.config['config_file'] = os.path.expanduser(self.config['config_file'])
        
        # Load or create state file
        self.state = self._load_state()
        
        # Ensure we have an API key
        if not self.config['api_key']:
            self._prompt_for_api_key()
            
        self.logger.info(f"Agent initialized to monitor {self.config['log_file']}")
        
    def _load_state(self) -> Dict[str, Any]:
        """Load agent state from file or create default state."""
        state_file = Path(self.config['state_file'])
        
        if state_file.exists():
            try:
                with open(state_file, 'r') as f:
                    state = json.load(f)
                self.logger.debug(f"Loaded state from {state_file}")
                return state
            except Exception as e:
                self.logger.warning(f"Failed to load state file: {e}")
        
        # Default state
        return {
            "last_position": 0,
            "last_run": datetime.datetime.now().isoformat()
        }
    
    def _save_state(self):
        """Save agent state to file."""
        state_file = Path(self.config['state_file'])
        
        try:
            # Update last run time
            self.state["last_run"] = datetime.datetime.now().isoformat()
            
            # Ensure directory exists
            state_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(state_file, 'w') as f:
                json.dump(self.state, f)
                
            self.logger.debug(f"Saved state to {state_file}")
        except Exception as e:
            self.logger.error(f"Failed to save state: {e}")
    
    def _save_config(self):
        """Save agent configuration to file."""
        config_file = Path(self.config['config_file'])
        
        try:
            # Ensure directory exists
            config_file.parent.mkdir(parents=True, exist_ok=True)
            
            # Save only necessary config items
            save_config = {
                "api_endpoint": self.config["api_endpoint"],
                "api_key": self.config["api_key"],
                "log_file": self.config["log_file"]
            }
            
            with open(config_file, 'w') as f:
                json.dump(save_config, f)
                
            # Secure the config file
            os.chmod(config_file, 0o600)
                
            self.logger.debug(f"Saved configuration to {config_file}")
        except Exception as e:
            self.logger.error(f"Failed to save configuration: {e}")
    
    def _prompt_for_api_key(self):
        """Prompt user for API key and save it."""
        try:
            import getpass
            print("\nAPI key is required for sending logs to the server.")
            api_key = getpass.getpass("Enter API key: ")
            
            if api_key:
                self.config['api_key'] = api_key
                self._save_config()
                self.logger.info("API key saved to configuration file")
            else:
                self.logger.error("No API key provided, agent will not be able to send logs")
        except Exception as e:
            self.logger.error(f"Failed to get API key: {e}")
    
    def _parse_syslog_line(self, line: str) -> Optional[Dict[str, Any]]:
        """Parse a syslog line into structured data."""
        try:
            match = SYSLOG_PATTERN.match(line)
            if not match:
                return None
                
            data = match.groupdict()
            
            # Parse timestamp
            timestamp_str = data.get('timestamp')
            if timestamp_str:
                # Add current year since syslog doesn't include it
                current_year = datetime.datetime.now().year
                try:
                    timestamp = datetime.datetime.strptime(f"{current_year} {timestamp_str}", "%Y %b %d %H:%M:%S")
                    # Handle year rollover
                    if timestamp > datetime.datetime.now():
                        timestamp = datetime.datetime.strptime(f"{current_year-1} {timestamp_str}", "%Y %b %d %H:%M:%S")
                    data['timestamp'] = timestamp.isoformat()
                except ValueError:
                    data['timestamp'] = timestamp_str
            
            # Use system hostname if not in log
            if not data.get('hostname'):
                data['hostname'] = self.hostname
                
            # Generate a unique log ID
            import uuid
            log_id = str(uuid.uuid4())
            
            # Create standardized log entry
            log_entry = {
                "logId": log_id,
                "timestamp": data.get('timestamp'),
                "source": "System",
                "sourceType": "syslog",
                "host": data.get('hostname'),
                "logLevel": self._guess_log_level(data.get('message', '')),
                "message": data.get('message', ''),
                "additionalFields": {
                    "process": data.get('process'),
                    "pid": data.get('pid'),
                    "raw": line.strip(),
                    "metadata": {
                        "collection_time": datetime.datetime.now().isoformat(),
                        "agent_version": "1.0.0"
                    }
                }
            }
            
            return log_entry
        except Exception as e:
            self.logger.warning(f"Failed to parse log line: {e}")
            return None
    
    def _guess_log_level(self, message: str) -> str:
        """Guess log level based on message content."""
        message = message.lower()
        
        if any(term in message for term in ['emergency', 'alert', 'critical', 'crit']):
            return 'critical'
        elif any(term in message for term in ['error', 'err', 'failed', 'failure']):
            return 'error'
        elif any(term in message for term in ['warn', 'warning']):
            return 'warning'
        elif any(term in message for term in ['notice', 'info', 'information']):
            return 'info'
        elif any(term in message for term in ['debug']):
            return 'debug'
        else:
            return 'info'  # Default level
    
    def _send_logs(self, logs: List[Dict[str, Any]]) -> bool:
        """Send logs to the API endpoint with retry logic."""
        if not logs:
            return True
            
        if not self.config['api_key']:
            self.logger.warning("No API key configured, cannot send logs")
            return False
            
        retry_count = 0
        max_retries = self.config['max_retries']
        backoff_factor = self.config['backoff_factor']
        
        payload = {"logs": logs}
        
        while retry_count <= max_retries:
            try:
                response = requests.post(
                    self.config['api_endpoint'],
                    json=payload,
                    headers={
                        'X-API-Key': self.config['api_key'],
                        'Content-Type': 'application/json'
                    },
                    timeout=30
                )
                
                # Handle response
                if response.status_code == 201:
                    self.logger.info(f"Successfully sent {len(logs)} logs to API")
                    return True
                elif 400 <= response.status_code < 500:
                    # Client errors should not be retried
                    self.logger.error(f"API error (client): {response.status_code} - {response.text}")
                    return False
                else:
                    # Server errors should be retried
                    self.logger.warning(f"API error (server): {response.status_code} - {response.text}")
                    
            except requests.RequestException as e:
                self.logger.warning(f"Request failed: {e}")
            
            # Exponential backoff
            retry_count += 1
            if retry_count <= max_retries:
                wait_time = backoff_factor ** retry_count
                self.logger.info(f"Retrying in {wait_time:.1f} seconds (attempt {retry_count}/{max_retries})")
                time.sleep(wait_time)
        
        self.logger.error(f"Failed to send logs after {max_retries} retries")
        return False
    
    def run(self):
        """Run the agent monitoring loop."""
        self.logger.info(f"Starting syslog monitoring for {self.config['log_file']}")
        
        try:
            # Check if log file exists
            if not os.path.exists(self.config['log_file']):
                self.logger.error(f"Log file not found: {self.config['log_file']}")
                return
                
            # Get initial file size if we don't have a position
            if self.state['last_position'] == 0:
                self.state['last_position'] = os.path.getsize(self.config['log_file'])
                self._save_state()
                
            while True:
                try:
                    current_size = os.path.getsize(self.config['log_file'])
                    
                    # Check if file was rotated (size decreased)
                    if current_size < self.state['last_position']:
                        self.logger.info("Log rotation detected, resetting position")
                        self.state['last_position'] = 0
                    
                    # Check if there's new data
                    if current_size > self.state['last_position']:
                        self._process_new_logs(current_size)
                    
                    # Sleep before next check
                    time.sleep(self.config['check_interval'])
                    
                except Exception as e:
                    self.logger.error(f"Error in monitoring loop: {e}")
                    time.sleep(self.config['check_interval'])
                    
        except KeyboardInterrupt:
            self.logger.info("Agent stopped by user")
        except Exception as e:
            self.logger.error(f"Agent error: {e}")
        finally:
            self._save_state()
    
    def _process_new_logs(self, current_size: int):
        """Process new log entries from the file."""
        logs_to_send = []
        
        try:
            with open(self.config['log_file'], 'r') as f:
                # Seek to last position
                f.seek(self.state['last_position'])
                
                # Read new lines
                for line in f:
                    if not line.strip():
                        continue
                        
                    log_entry = self._parse_syslog_line(line)
                    if log_entry:
                        logs_to_send.append(log_entry)
                        
                    # Send in batches if we've reached max batch size
                    if len(logs_to_send) >= self.config['max_batch_size']:
                        self._send_logs(logs_to_send)
                        logs_to_send = []
                
                # Update position
                self.state['last_position'] = f.tell()
            
            # Send any remaining logs
            if logs_to_send:
                self._send_logs(logs_to_send)
                
            # Save state after successful processing
            self._save_state()
            
        except Exception as e:
            self.logger.error(f"Error processing logs: {e}")


def load_config(args):
    """Load configuration from defaults, config file, and command line arguments."""
    config = DEFAULT_CONFIG.copy()
    
    # Load from config file if it exists
    config_file = os.path.expanduser(args.config or DEFAULT_CONFIG['config_file'])
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r') as f:
                file_config = json.load(f)
                config.update(file_config)
        except Exception as e:
            print(f"Warning: Failed to load config file: {e}")
    
    # Override with command line arguments
    if args.log_file:
        config['log_file'] = args.log_file
    if args.api_endpoint:
        config['api_endpoint'] = args.api_endpoint
    if args.api_key:
        config['api_key'] = args.api_key
    if args.interval:
        config['check_interval'] = args.interval
    if args.batch_size:
        config['max_batch_size'] = args.batch_size
    
    # Debug settings
    config['debug'] = args.debug
    config['log_to_file'] = args.log_to_file
    if args.error_log:
        config['error_log'] = args.error_log
    
    return config


def main():
    """Main entry point for the agent."""
    parser = argparse.ArgumentParser(description='Linux Syslog Monitoring Agent')
    
    parser.add_argument('--config', help='Path to configuration file')
    parser.add_argument('--log-file', help='Path to syslog file to monitor')
    parser.add_argument('--api-endpoint', help='API endpoint URL')
    parser.add_argument('--api-key', help='API key for authentication')
    parser.add_argument('--interval', type=float, help='Check interval in seconds')
    parser.add_argument('--batch-size', type=int, help='Maximum batch size for sending logs')
    parser.add_argument('--debug', action='store_true', help='Enable debug logging')
    parser.add_argument('--log-to-file', action='store_true', help='Log errors to file')
    parser.add_argument('--error-log', help='Path to error log file')
    
    args = parser.parse_args()
    
    # Load configuration
    config = load_config(args)
    
    # Create and run agent
    agent = SyslogAgent(config)
    agent.run()


if __name__ == '__main__':
});
