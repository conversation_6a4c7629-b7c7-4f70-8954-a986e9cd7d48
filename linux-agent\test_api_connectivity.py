#!/usr/bin/env python3
"""
API Connectivity Test Script

Tests connectivity between the Linux agent and the ExLog dashboard API.
"""

import sys
import json
import time
import requests
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from config.config_manager import ConfigManager
from utils.api_client import ExLogAPIClient


def test_basic_connectivity(endpoint):
    """Test basic HTTP connectivity to the endpoint."""
    print(f"🔗 Testing basic connectivity to {endpoint}...")
    
    try:
        # Test basic connectivity
        response = requests.get(f"{endpoint}/api/v1/health", timeout=10)
        print(f"   ✅ Basic connectivity: {response.status_code}")
        return True
    except requests.exceptions.ConnectionError:
        print(f"   ❌ Connection failed: Cannot reach {endpoint}")
        return False
    except requests.exceptions.Timeout:
        print(f"   ❌ Connection timeout: {endpoint} took too long to respond")
        return False
    except Exception as e:
        print(f"   ❌ Unexpected error: {e}")
        return False


def test_api_key_auth(endpoint, api_key):
    """Test API key authentication."""
    print(f"🔑 Testing API key authentication...")
    
    try:
        headers = {
            'X-API-Key': api_key,
            'Content-Type': 'application/json'
        }
        
        # Test with a simple log entry
        test_log = {
            "logs": [{
                "logId": "test_connectivity_log_001",
                "timestamp": time.strftime('%Y-%m-%dT%H:%M:%S.000Z', time.gmtime()),
                "source": "System",
                "sourceType": "event",
                "host": "test-connectivity",
                "logLevel": "info",
                "message": "API connectivity test log"
            }]
        }
        
        response = requests.post(
            f"{endpoint}/api/v1/logs",
            json=test_log,
            headers=headers,
            timeout=10
        )
        
        if response.status_code == 200:
            print(f"   ✅ API key authentication successful")
            print(f"   📊 Response: {response.json()}")
            return True
        elif response.status_code == 401:
            print(f"   ❌ API key authentication failed: Invalid API key")
            return False
        else:
            print(f"   ❌ API error: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ API key test error: {e}")
        return False


def test_api_client(config):
    """Test the ExLogAPIClient."""
    print(f"🚀 Testing ExLogAPIClient...")
    
    try:
        client = ExLogAPIClient(config['api'])
        
        # Create test logs
        test_logs = [{
            "logId": "test_client_log_001",
            "timestamp": time.strftime('%Y-%m-%dT%H:%M:%S.000Z', time.gmtime()),
            "source": "System",
            "sourceType": "event",
            "host": "test-client",
            "logLevel": "info",
            "message": "ExLogAPIClient test log"
        }]
        
        success = client.send_logs(test_logs)
        
        if success:
            print(f"   ✅ ExLogAPIClient test successful")
            print(f"   📊 Client stats: {client.get_statistics()}")
            return True
        else:
            print(f"   ❌ ExLogAPIClient test failed")
            print(f"   📊 Client stats: {client.get_statistics()}")
            return False
            
    except Exception as e:
        print(f"   ❌ ExLogAPIClient error: {e}")
        return False


def suggest_fixes(endpoint):
    """Suggest potential fixes based on the endpoint."""
    print(f"\n🔧 Suggested fixes:")
    
    if "localhost" in endpoint or "127.0.0.1" in endpoint:
        print(f"   1. The endpoint '{endpoint}' uses localhost/127.0.0.1")
        print(f"      This won't work from a VM to the host machine.")
        print(f"      Try using the host machine's IP address instead.")
        print(f"      Example: http://*************:5000 (replace with your host IP)")
        
    print(f"   2. Check if the dashboard backend is running on the host")
    print(f"   3. Check firewall settings on the host machine")
    print(f"   4. Verify the API key is correct")
    print(f"   5. Check if the port (5000) is accessible from the VM")


def main():
    """Main test function."""
    print("🧪 ExLog API Connectivity Test")
    print("=" * 50)
    
    try:
        # Load configuration
        print("📋 Loading configuration...")
        config_manager = ConfigManager()
        config = config_manager.load_config()
        
        endpoint = config['api']['endpoint']
        api_key = config['api']['api_key']
        
        print(f"   📍 Endpoint: {endpoint}")
        print(f"   🔑 API Key: {api_key[:20]}..." if len(api_key) > 20 else f"   🔑 API Key: {api_key}")
        print()
        
        # Run tests
        tests_passed = 0
        total_tests = 3
        
        if test_basic_connectivity(endpoint):
            tests_passed += 1
            
        if test_api_key_auth(endpoint, api_key):
            tests_passed += 1
            
        if test_api_client(config):
            tests_passed += 1
        
        # Results
        print(f"\n📊 Test Results: {tests_passed}/{total_tests} tests passed")
        
        if tests_passed == total_tests:
            print("🎉 All tests passed! The agent should be able to send logs to the dashboard.")
        else:
            print("❌ Some tests failed. The agent may not be able to send logs properly.")
            suggest_fixes(endpoint)
        
        return 0 if tests_passed == total_tests else 1
        
    except Exception as e:
        print(f"❌ Test script error: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
